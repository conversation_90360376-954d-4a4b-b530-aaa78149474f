/*
 评论页面需求说明（2024-07-10）

 1. 仅展示一级评论（不自动展示二级评论列表）。
 2. 一级评论模型字段 isHasChild==true 时，底部展示"展开N条回复"按钮（N=childCount），样式如"展开99条回复↓"。
 3. 点击"展开N条回复"时，异步拉取2条二级评论，拼接到当前一级评论下方，继续点击则每次再拉2条，直到全部加载完毕。
 4. 一级评论支持图片展示：commentImg 字段有值时，展示一张图片（用 Kingfisher 加载）。
 5. 二级评论同样支持图片展示（如有 commentImg 字段）。
 6. 点赞按钮右侧新增"不喜欢"按钮，UI与点赞按钮一致，点击后触发相应回调。
 7. 不展示二级评论的独立页面，所有二级评论都在一级评论下方动态拼接。
 8. 一级评论和二级评论都需支持点赞/不喜欢操作。
 9. 一级评论和二级评论都需支持图片展示（如有）。
 10. 作者标签：若评论用户为视频作者，用户名右侧展示"作者"标签。
 11. @功能：若pcommentUserVo存在，用户名后展示"@被回复人昵称"，样式需区分。
 12. 其他UI细节严格参考产品设计图。
*/
import UIKit
import IQTextView
import IQKeyboardManagerSwift
import PhotosUI
import Kingfisher // 新增

class VideoCommentViewController: UIViewController, UITableViewDelegate, UITableViewDataSource, UIGestureRecognizerDelegate, UITextViewDelegate, PHPickerViewControllerDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    // MARK: - Properties
    private var videoId: Int = 0   // 作品 ID，用于拉取评论

    private var commentCount: Int = 0 // 初始评论总数，由视频模型提供

    // 视频相关数据，用于中间层显示
    private var videoLikeCount: Int = 0
    private var videoCollectCount: Int = 0

    private var comments: [CommentModel] = []
    private var isSending = false // 发送中标记
    private var pageSize = 20
    private var lastCommentId: Int? = nil
    private var lastCreateTime: String? = nil
    private var isLoading = false
    private var hasMoreData = true

    // 底部提示
    private let footerLabel: UILabel = {
        let label = UILabel()
        label.text = "到底了~"
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.frame = CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44)
        label.isHidden = true
        return label
    }()
    
    // 新增：二级评论 Cell 标识
    private let replyCellIdentifier = "CommentReplyCell"
    
    // 新增：背景遮罩视图
    private var backgroundView: UIView!
    
    // MARK: - Row Mapping
    private enum RowType { case comment, reply, expand }

    /// 生成显示行映射：[(RowType, indexInComments)]
    private func makeRowMap() -> [(RowType, Int)] {
        var rows: [(RowType, Int)] = []
        var idx = 0
        while idx < comments.count {
            let model = comments[idx]
            if model.level == 0 {
                rows.append((.comment, idx))
                var next = idx + 1
                while next < comments.count, comments[next].level == 1 {
                    rows.append((.reply, next))
                    next += 1
                }
                if model.isHasChild && model.showExpandReplies {
                    rows.append((.expand, idx)) // parent idx reference
                }
                idx = next
            } else {
                rows.append((.reply, idx))
                idx += 1
            }
        }
        return rows
    }

    private var cachedRowMap: [(RowType, Int)] { makeRowMap() }
    
    // MARK: - Initializer
    init(videoId: Int = 0, initialCount: Int = 0, likeCount: Int = 0, collectCount: Int = 0) {
        self.videoId = videoId
        self.commentCount = initialCount
        self.videoLikeCount = likeCount
        self.videoCollectCount = collectCount
        super.init(nibName: nil, bundle: nil)
    }

    @available(*, unavailable)
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.layer.cornerRadius = 24
        view.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        view.clipsToBounds = true
        return view
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "评论 \(commentCount)"
        label.font = .boldSystemFont(ofSize: 18)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    private lazy var closeButton: UIButton = {
        let button = UIButton(type: .custom)
        button.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.delegate = self
        table.dataSource = self
        table.backgroundColor = UIColor(hex: "#F5F5F5")
        table.separatorStyle = .none
        table.register(CommentCell.self, forCellReuseIdentifier: "CommentCell")
        table.register(CommentReplyCell.self, forCellReuseIdentifier: replyCellIdentifier)
        table.register(ExpandRepliesCell.self, forCellReuseIdentifier: "ExpandRepliesCell")
        table.estimatedRowHeight = 140
        table.rowHeight = UITableView.automaticDimension
        return table
    }()
    
    // MARK: - Pull Indicator
    private lazy var pullIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#DDDDDD")
        view.layer.cornerRadius = 2
        return view
    }()
    
    // MARK: - 评论输入区（新版）
    private lazy var commentInputBar: UIView = {
        let view = UIView()
//        view.backgroundColor =
        view.backgroundColor = .white
//        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true

        // 输入框
        let inputField = IQTextView()
        inputField.backgroundColor = UIColor(hex: "#000000", alpha: 0.1)
        inputField.textColor = UIColor(hex: "#000000")
        inputField.layer.cornerRadius = 12
        inputField.font = .systemFont(ofSize: 14)
        inputField.placeholder = "说点什么..."
        inputField.placeholderTextColor = UIColor(hex: "#000000", alpha: 0.45)
        inputField.returnKeyType = .send
        inputField.textContainerInset = UIEdgeInsets(top: 9, left: 10, bottom: 9, right: 10)
        inputField.delegate = self
        view.addSubview(inputField)
        inputFieldRef = inputField
        // 添加监听输入框编辑开始事件
        // inputField.addTarget(self, action: #selector(inputFieldDidBeginEditing), for: .editingDidBegin)
        inputField.snp.makeConstraints { make in
            make.top.equalTo(view).offset(9)
            make.left.equalTo(view).offset(18)
            make.right.equalTo(view).offset(-18)
            make.height.equalTo(72)
        }

        // @按钮
        let atButton = UIButton(type: .system)
        atButton.setImage(UIImage(named: "icon_input_AtButton"), for: .normal)
        atButton.tintColor = .gray
        view.addSubview(atButton)
        atButton.addTarget(self, action: #selector(atButtonTapped), for: .touchUpInside)
        atButton.snp.makeConstraints { make in
            make.top.equalTo(inputField.snp.bottom).offset(9)
            make.left.equalTo(inputField)
            make.width.height.equalTo(24)
        }

        // 图片按钮
        let imageButton = UIButton(type: .system)
        imageButton.setImage(UIImage(named: "icon_input_ImageButton"), for: .normal)
        imageButton.tintColor = .gray
        view.addSubview(imageButton)
        imageButton.snp.makeConstraints { make in
            make.top.equalTo(atButton)
            make.left.equalTo(atButton.snp.right).offset(15)
            make.width.height.equalTo(24)
        }
        imageButton.addTarget(self, action: #selector(imageButtonTapped), for: .touchUpInside)

        // 表情按钮
        let emojiButton = UIButton(type: .system)
        emojiButton.setImage(UIImage(named: "icon_input_EmotionButton"), for: .normal)
        emojiButton.tintColor = .gray
        view.addSubview(emojiButton)
        emojiButton.snp.makeConstraints { make in
            make.top.equalTo(atButton)
            make.left.equalTo(imageButton.snp.right).offset(15)
            make.width.height.equalTo(24)
        }
        emojiButton.addTarget(self, action: #selector(emojiButtonTapped), for: .touchUpInside)

        // 发送按钮
        let sendButton = UIButton(type: .system)
        sendButton.setTitle("发送", for: .normal)
        sendButton.setTitleColor(.white, for: .normal)
        sendButton.backgroundColor = UIColor(hex: "#FF8F1F", alpha: 0.5)
        sendButton.layer.cornerRadius = 15.5
        sendButton.layer.masksToBounds = true
        sendButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        sendButton.isEnabled = false // 初始状态为禁用
        view.addSubview(sendButton)
        sendButtonRef = sendButton
        sendButton.snp.makeConstraints { make in
            make.top.equalTo(inputField.snp.bottom).offset(9)
            make.right.equalTo(inputField)
            make.width.equalTo(55)
            make.height.equalTo(31)
        }
        sendButton.addTarget(self, action: #selector(sendButtonTapped), for: .touchUpInside)

        // 图片预览容器（默认隐藏）
        let previewContainer = UIView()
        previewContainer.isHidden = true
        previewContainer.layer.cornerRadius = 4
        previewContainer.clipsToBounds = true
        view.addSubview(previewContainer)
        previewContainer.snp.makeConstraints { make in
            make.top.equalTo(atButton.snp.bottom).offset(16)
            make.left.equalTo(atButton)
            make.width.height.equalTo(40)
        }
        self.imagePreviewContainer = previewContainer

        let previewImageView = UIImageView()
        previewImageView.contentMode = .scaleAspectFill
        previewImageView.clipsToBounds = true
        previewContainer.addSubview(previewImageView)
        previewImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        self.imagePreviewImageView = previewImageView

        // 关闭预览按钮
        let closeBtn = UIButton(type: .custom)
        // 使用指定的清除图标资源
        closeBtn.setImage(UIImage(named: "comment_claer"), for: .normal)
        closeBtn.isHidden = true
        closeBtn.addTarget(self, action: #selector(removeImagePreview), for: .touchUpInside)
        view.addSubview(closeBtn)
        closeBtn.snp.makeConstraints { make in
            make.width.height.equalTo(14)
            make.centerY.equalTo(previewContainer.snp.top)
            make.centerX.equalTo(previewContainer.snp.right)
        }
        self.imagePreviewCloseButton = closeBtn

        return view
    }()

    // MARK: - 中间层装饰输入框
    private var decorativeLikeCountLabel: UILabel!
    private var decorativeCollectCountLabel: UILabel!

    private lazy var decorativeInputBar: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = false // 默认显示

        // 装饰输入框
        let inputContainer = UIView()
        inputContainer.backgroundColor = UIColor(hex: "#EDEDED")
        inputContainer.layer.cornerRadius = 17.5
        view.addSubview(inputContainer)

        // 编辑图标
        let editIcon = UIImageView()
        editIcon.image = UIImage(named: "video_comment_edit")
        editIcon.contentMode = .scaleAspectFit
        inputContainer.addSubview(editIcon)
        editIcon.snp.makeConstraints { make in
            make.left.equalTo(inputContainer).offset(12)
            make.centerY.equalTo(inputContainer)
            make.width.height.equalTo(15)
        }

        // 占位文字
        let placeholderLabel = UILabel()
        placeholderLabel.text = "说点什么吧..."
        placeholderLabel.font = .systemFont(ofSize: 13)
        placeholderLabel.textColor = UIColor(hex: "#777777")
        inputContainer.addSubview(placeholderLabel)
        placeholderLabel.snp.makeConstraints { make in
            make.left.equalTo(editIcon.snp.right).offset(8)
            make.centerY.equalTo(inputContainer)
        }

        // 点赞图标
        let likeIcon = UIImageView()
        likeIcon.image = UIImage(named: "icon_like") // 需要替换为实际的点赞图标名称
        likeIcon.contentMode = .scaleAspectFit
        view.addSubview(likeIcon)
        likeIcon.snp.makeConstraints { make in
            make.right.equalTo(view).offset(-12)
            make.centerY.equalTo(inputContainer)
            make.width.height.equalTo(23)
        }

        // 点赞数量
        let likeCountLabel = UILabel()
        likeCountLabel.text = "\(videoLikeCount)"
        likeCountLabel.font = .systemFont(ofSize: 13)
        likeCountLabel.textColor = UIColor(hex: "#777777")
        view.addSubview(likeCountLabel)
        likeCountLabel.snp.makeConstraints { make in
            make.right.equalTo(likeIcon.snp.left).offset(-4)
            make.centerY.equalTo(inputContainer)
        }
        self.decorativeLikeCountLabel = likeCountLabel

        // 收藏图标
        let collectIcon = UIImageView()
        collectIcon.image = UIImage(named: "icon_collect") // 需要替换为实际的收藏图标名称
        collectIcon.contentMode = .scaleAspectFit
        view.addSubview(collectIcon)
        collectIcon.snp.makeConstraints { make in
            make.right.equalTo(likeCountLabel.snp.left).offset(-8)
            make.centerY.equalTo(inputContainer)
            make.width.height.equalTo(23)
        }

        // 收藏数量
        let collectCountLabel = UILabel()
        collectCountLabel.text = "\(videoCollectCount)"
        collectCountLabel.font = .systemFont(ofSize: 13)
        collectCountLabel.textColor = UIColor(hex: "#777777")
        view.addSubview(collectCountLabel)
        collectCountLabel.snp.makeConstraints { make in
            make.right.equalTo(collectIcon.snp.left).offset(-4)
            make.centerY.equalTo(inputContainer)
        }
        self.decorativeCollectCountLabel = collectCountLabel

        // 装饰输入框约束
        inputContainer.snp.makeConstraints { make in
            make.left.equalTo(view).offset(12)
            make.top.equalTo(view).offset(15)
            make.right.equalTo(collectCountLabel.snp.left).offset(-10)
            make.height.equalTo(35)
        }

        // 添加点击手势
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(decorativeInputTapped))
        inputContainer.addGestureRecognizer(tapGesture)
        inputContainer.isUserInteractionEnabled = true

        return view
    }()

    // 替换原有commentInputView相关布局为新版commentInputBar
    private var commentInputBarBottomConstraint: NSLayoutConstraint?
    private var commentInputBarHeightConstraint: NSLayoutConstraint?
    private var decorativeInputBarBottomConstraint: NSLayoutConstraint?
    private var decorativeInputBarHeightConstraint: NSLayoutConstraint?
    
    // MARK: - EmojiKeyboardView
    class EmojiKeyboardView: UIView, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        var emojiSelected: ((String) -> Void)?
        private let emojis = ["😀","😁","😂","🤣","😃","😄","😅","😆","😉","😊","😋","😎","😍","😘","🥰","😗","😙","😚","🙂","🤗","🤩","🤔","🤨","😐","😑","😶","🙄","😏","😣","😥","😮","🤐","😯","😪","😫","😴","😌","😛","😜","😝","🤤","😒","😓","😔","😕","🙃","🤑","😲","☹️","🙁","😖","😞","😟","😤","😢","😭","😦","😧","😨","😩","🤯","😬","😰","😱","🥵","🥶","😳","🤪","😵","😡","😠","🤬","😷","🤒","🤕","🤢","🤮","🤧","😇","🥳","🥺","🤠","🤡","🤥","🤫","🤭","🧐","🤓"]
        private let collectionView: UICollectionView
        
        override init(frame: CGRect) {
            let layout = UICollectionViewFlowLayout()
            let itemWidth = UIScreen.main.bounds.width / 8
            layout.itemSize = CGSize(width: itemWidth, height: 44)
            layout.minimumInteritemSpacing = 0
            layout.minimumLineSpacing = 0
            layout.sectionInset = .zero
            collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
            super.init(frame: frame)
            collectionView.dataSource = self
            collectionView.delegate = self
            collectionView.register(UICollectionViewCell.self, forCellWithReuseIdentifier: "EmojiCell")
            collectionView.backgroundColor = .clear
            addSubview(collectionView)
            collectionView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                collectionView.topAnchor.constraint(equalTo: topAnchor),
                collectionView.bottomAnchor.constraint(equalTo: bottomAnchor),
                collectionView.leadingAnchor.constraint(equalTo: leadingAnchor),
                collectionView.trailingAnchor.constraint(equalTo: trailingAnchor)
            ])
        }
        required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
        
        func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int { emojis.count }
        func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "EmojiCell", for: indexPath)
            let label = UILabel()
            label.text = emojis[indexPath.item]
            label.font = .systemFont(ofSize: 28)
            label.textAlignment = .center
            cell.contentView.subviews.forEach { $0.removeFromSuperview() }
            cell.contentView.addSubview(label)
            label.frame = cell.contentView.bounds
            return cell
        }
        func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
            emojiSelected?(emojis[indexPath.item])
        }
    }

    // VideoCommentViewController 内部属性
    private var emojiKeyboardView: EmojiKeyboardView?
    private var lastKeyboardHeight: CGFloat = 291
    private var isEmojiKeyboardVisible = false
    private var inputFieldRef: IQTextView? = nil
    private var sendButtonRef: UIButton? = nil
    private var isSwitchingToEmojiKeyboard = false

    // MARK: - Image Preview Support
    private var selectedImage: UIImage? = nil // 选中的待发送图片
    private var imagePreviewContainer: UIView? = nil
    private var imagePreviewImageView: UIImageView? = nil
    private var imagePreviewCloseButton: UIButton? = nil
    private let previewExtraHeight: CGFloat = 56 // 40 + 16

    // MARK: - Reply Target Tracking
    private let defaultPlaceholder = "说点什么..."
    private var replyTargetUsername: String? = nil {
        didSet {
            if let name = replyTargetUsername, !name.isEmpty {
                inputFieldRef?.placeholder = "回复 @\(name)"
            } else {
                inputFieldRef?.placeholder = defaultPlaceholder
            }
        }
    }
    private var replyPid: Int? = nil
    private var replyPcustomerId: String? = nil
    private var selectedParentIndex: Int?
    private var clickedIndex: Int?
    
    // MARK: - @ 提及搜索面板相关
    private var atPanelView: UIView?
    private var mentionCollectionView: UICollectionView?
    private var mentionUsersData: [UserSearchResultsItem] = []
    private var mentionKeyword: String = ""
    private var mentionPage: Int = 0
    private var mentionHasMore: Bool = true
    private var mentionTotal: Int = 0
    private let mentionPageSize: Int = 10
    private let mentionPlaceholder: String = "输入搜索@的人"
    private var debounceWorkItem: DispatchWorkItem?
    private var isMentioning: Bool = false
    private var mentionStartLocation: Int = 0
    private var mentionedUserDict: [String: String] = [:]
    
    // MARK: - 子评论分页追踪
    // 二级评论游标记录：key 为父评论 id，value 为(最后一条回复 id, 最后一条回复 createTime 字符串, 已加载数量)
    private var replyCursorTracker: [String: (lastId: Int?, lastTime: String?, loadedCount: Int)] = [:]
    
    // MARK: - 输入面板状态管理
    private enum InputPanelState {
        case none           // 全部收起
        case keyboard       // 文字键盘弹出
        case emoji          // 表情盘弹出
        case atPanel        // 艾特面板弹出（预留）
    }

    private var inputPanelState: InputPanelState = .none {
        didSet {
            // 仅在状态真正变化时处理，避免后台→前台产生大量重复日志
            guard oldValue != inputPanelState else { return }
            print("inputPanelState changed from \(oldValue) to \(inputPanelState)")
            updateInputPanelState(from: oldValue, to: inputPanelState)
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        // 只有在初始化时传入有效videoId才加载评论
        if videoId > 0 {
            loadComments()
        }
        setupKeyboardObservers()

        // 设置tableView的调试边框
        tableView.layer.borderWidth = 0
        tableView.layer.borderColor = UIColor.red.cgColor

        // 初始化发送按钮状态
        updateSendButtonState()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        IQKeyboardManager.shared.isEnabled = false
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        IQKeyboardManager.shared.isEnabled = true
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        // 动态修正输入bar高度，保证safeAreaBottom正确
        switch inputPanelState {
        case .none:
            let baseHeight: CGFloat = 132 + (selectedImage != nil ? previewExtraHeight : 0)
            commentInputBarHeightConstraint?.constant = baseHeight + WindowUtil.safeAreaBottom
        default:
            let baseHeight: CGFloat = 132 + (selectedImage != nil ? previewExtraHeight : 0)
            commentInputBarHeightConstraint?.constant = baseHeight
        }
        // 立即刷新布局
        view.layoutIfNeeded()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 修改背景为透明
        view.backgroundColor = .clear
        
        // 新增: 添加半透明背景遮罩视图
        backgroundView = UIView()
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.01)
        backgroundView.frame = self.view.bounds
        backgroundView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        view.addSubview(backgroundView)
        
        // 添加点击手势到背景遮罩
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(backgroundTapped))
        tapGesture.delegate = self
        backgroundView.addGestureRecognizer(tapGesture)
        
        // containerView放在背景遮罩之上
        view.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            // 修改顶部距离为60pt
            make.top.equalTo(view.safeAreaLayoutGuide).offset(60)
            make.left.right.bottom.equalToSuperview()
        }
        
        // 添加灰色长条
        containerView.addSubview(pullIndicator)
        pullIndicator.snp.makeConstraints { make in
            make.top.equalTo(12)
            make.centerX.equalToSuperview()
            make.width.equalTo(36)
            make.height.equalTo(4)
        }
        
        containerView.addSubview(headerView)
        headerView.snp.makeConstraints { make in
            // 调整header位置，在长条下方
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.height.equalTo(80)
        }
        
        // 其他UI保持不变
        headerView.addSubview(titleLabel)
        headerView.addSubview(closeButton)
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        closeButton.snp.makeConstraints { make in
            make.left.right.top.bottom.equalToSuperview()
        }
        
        // 添加装饰输入区（默认显示）
        containerView.addSubview(decorativeInputBar)
        decorativeInputBar.translatesAutoresizingMaskIntoConstraints = false
        decorativeInputBarBottomConstraint = decorativeInputBar.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
        decorativeInputBarHeightConstraint = decorativeInputBar.heightAnchor.constraint(equalToConstant: 65 + WindowUtil.safeAreaBottom)
        NSLayoutConstraint.activate([
            decorativeInputBar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            decorativeInputBar.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            decorativeInputBarBottomConstraint!,
            decorativeInputBarHeightConstraint!
        ])

        // 添加真实输入区（默认隐藏）
        containerView.addSubview(commentInputBar)
        commentInputBar.translatesAutoresizingMaskIntoConstraints = false
        commentInputBar.isHidden = true // 默认隐藏
        commentInputBarBottomConstraint = commentInputBar.bottomAnchor.constraint(equalTo: containerView.bottomAnchor)
        commentInputBarHeightConstraint = commentInputBar.heightAnchor.constraint(equalToConstant: 132 + WindowUtil.safeAreaBottom)
        NSLayoutConstraint.activate([
            commentInputBar.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            commentInputBar.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            commentInputBarBottomConstraint!,
            commentInputBarHeightConstraint!
        ])
        
        tableView.tableFooterView = footerLabel
        // 确保tableView被add到containerView上
        containerView.addSubview(tableView)
        // tableView底部约束改为装饰输入区顶部
        tableView.snp.remakeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalTo(containerView)
            make.bottom.equalTo(decorativeInputBar.snp.top)
        }
        
        // 添加下拉手势
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        containerView.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Actions
    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }
    
    @objc private func backgroundTapped() {
        // 直接关闭弹窗
        dismiss(animated: true)
    }

    @objc private func decorativeInputTapped() {
        // 隐藏装饰输入框
        decorativeInputBar.isHidden = true

        // 显示真实输入框
        commentInputBar.isHidden = false

        // 更新tableView约束到真实输入框
        tableView.snp.remakeConstraints { make in
            make.top.equalTo(headerView.snp.bottom)
            make.left.right.equalTo(containerView)
            make.bottom.equalTo(commentInputBar.snp.top)
        }

        // 聚焦输入框并弹出键盘
        inputFieldRef?.becomeFirstResponder()
        inputPanelState = .keyboard

        // 动画更新布局
        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded()
        }
    }
    
    // MARK: - Data Loading
    func showComments(videoId: Int) {
        // 更新当前视频ID
        self.videoId = videoId
        
        // 重置页码和数据状态
        self.pageSize = 20
        self.lastCommentId = nil
        self.lastCreateTime = nil
        self.comments = []
        self.hasMoreData = true
        
        // 加载第一页评论
        loadComments()
    }

    // 更新视频数据（点赞数、收藏数）
    func updateVideoData(likeCount: Int, collectCount: Int) {
        self.videoLikeCount = likeCount
        self.videoCollectCount = collectCount

        // 更新装饰输入框中的数据显示
        decorativeLikeCountLabel?.text = "\(likeCount)"
        decorativeCollectCountLabel?.text = "\(collectCount)"
    }

    // 保留原有loadComments作为内部方法
    private func loadComments() {
        guard !isLoading && hasMoreData else { return }
        isLoading = true
        
        print("[CommentVC] 开始加载评论，videoId=\(videoId), fromId=\(self.lastCommentId ?? 0), createTime=\(self.lastCreateTime ?? "")")

        APIManager.shared.getVideoComment(
            worksId: videoId,
            size: pageSize,
            pid: nil,
            excludeId: nil,
            fromId: lastCommentId,
            createTime: lastCreateTime
        ) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        let items = response.data.list
                        // 提取全局 mentionedUser
                        var globalMap: [String:String] = [:]
                        if let anyMap = response.data.mentionedUser {
                            if let dict = anyMap as? [String:String] {
                                globalMap = dict
                            } else if let dict = anyMap as? [String:Any] {
                                for (k,v) in dict { if let name = v as? String { globalMap[k] = name } }
                            }
                        }
                        let models = items.map { self.convertToCommentModel($0, level: 0, mentionMapping: globalMap) }

                        if self.comments.isEmpty {
                            self.comments = models
                        } else {
                            self.comments += models
                        }

                        // 更新分页游标
                        if let last = items.last {
                            self.lastCommentId = last.id
                            self.lastCreateTime = last.createTime
                        }

                        // 判断是否还有更多
                        self.hasMoreData = !items.isEmpty
                        self.footerLabel.isHidden = self.hasMoreData

                        // 接口已不返回 total，不再更新标题
                        self.tableView.reloadData()
                        
                        // 如果有数据但tableView没显示，检查布局问题
                        if !self.comments.isEmpty && self.tableView.visibleCells.isEmpty {
                            print("[CommentVC] 警告: 有评论数据但没有可见单元格，可能存在布局问题")
                            print("[CommentVC] tableView frame: \(self.tableView.frame)")
                            print("[CommentVC] tableView contentSize: \(self.tableView.contentSize)")
                        }
                    } else {
                        print("[CommentVC] 获取评论失败: \(response.errMsg)")
                        self.hasMoreData = false
                    }
                case .failure(let error):
                    print("[CommentVC] 加载评论错误: \(error)")
                    self.hasMoreData = false
                }
            }
        }
    }
    
    // MARK: - Mapping
    private func convertToCommentModel(_ item: CommentItem, level: Int = 0, mentionMapping: [String:String] = [:]) -> CommentModel {
        let avatar = item.commentUserVo?.wxAvator ?? "default_avatar"
        let username = item.commentUserVo?.nickName ?? "匿名用户"
        let customerId = item.commentUserVo?.customerId ?? ""

        // 处理 @ 提及
        var mapping: [String: String] = mentionMapping
        // 如果 item 内也带有 mentionedUser，则优先合并/覆盖
        if let mapAny = item.mentionedUser {
            if let dict = mapAny as? [String: String] {
                mapping.merge(dict) { _, new in new }
            } else if let dict = mapAny as? [String: Any] {
                for (k, v) in dict { if let name = v as? String { mapping[k] = name } }
            }
        }
        let mentionResult = parseMentions(in: item.commentDesc, mapping: mapping)
        let displayText = mentionResult.0
        let mentionRanges = mentionResult.1
        let replyTo = item.pcommentUserVo?.nickName
        // 判断是否为作者本人评论（根据 commentUserVo.customerId 与作品作者 ID 比较，可根据业务需要调整）
        // 使用接口新增字段 isCommentCustomerWorks 判定是否为作者评论
        let isAuthorComment = item.isCommentCustomerWorks

        let showExpandBtn = item.isHasChild && item.childCount > 0

        var model = CommentModel(
            id: String(item.id),
            customerId: customerId,
            avatar: avatar,
            username: username,
            isAuthor: isAuthorComment,
            content: displayText,
            timestamp: formatRelativeTime(item.createTime),
            likes: item.likeNumber,
            dislikes: item.notLikeNumber,
            isLiked: item.likeState == 1,
            isDisliked: item.notLikeState == 1,
            imageUrl: parseFirstImageURL(from: item.commentImg),
            replyTo: replyTo,
            isHasChild: item.isHasChild,
            childCount: item.childCount,
            replies: nil,
            showExpandReplies: showExpandBtn,
            level: level,
            cellHeight: 0,
            mentionRanges: mentionRanges,
            isMine: item.isMyComment
        )

        model.cellHeight = calculateCellHeight(text: displayText, hasImage: (model.imageUrl != nil && !(model.imageUrl!.isEmpty)), level: level, showExpandReplies: showExpandBtn)
        return model
    }

    // MARK: - 解析 commentImg
    private func parseFirstImageURL(from jsonArrayString: String?) -> String? {
        guard let jsonArrayString = jsonArrayString, !jsonArrayString.isEmpty else { return nil }
        // 如果不是 '[' 开头，直接返回原字符串（兼容老数据）
        guard jsonArrayString.trimmingCharacters(in: .whitespaces).first == "[" else {
            return jsonArrayString
        }
        // 尝试解析 JSON 数组
        if let data = jsonArrayString.data(using: .utf8),
           let array = try? JSONSerialization.jsonObject(with: data, options: []) as? [String],
           let first = array.first, !first.isEmpty {
            return first
        }
        return nil
    }

    // MARK: - 时间格式化："刚刚" / "x分钟前" / "x小时前" / "x天前" / "MM-dd" / "yyyy-MM-dd"
    private func formatRelativeTime(_ timeString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.locale = Locale(identifier: "zh_CN")
        guard let date = formatter.date(from: timeString) else { return timeString }
        let now = Date()
        let interval = now.timeIntervalSince(date)
        if interval < 60 { return "刚刚" }
        let minute = 60.0
        let hour = 3600.0
        let day = 86400.0
        if interval < hour { return "\(Int(interval/minute))分钟前" }
        if interval < day { return "\(Int(interval/hour))小时前" }
        if interval < day * 7 { return "\(Int(interval/day))天前" }
        // 当年显示月日，否则完整年月日
        let output = DateFormatter()
        output.locale = formatter.locale
        if Calendar.current.isDate(date, equalTo: now, toGranularity: .year) {
            output.dateFormat = "MM-dd"
        } else {
            output.dateFormat = "yyyy-MM-dd"
        }
        return output.string(from: date)
    }

    /// 计算行高：
    /// - Parameters:
    ///   - text: 评论文本
    ///   - hasImage: 是否包含图片
    ///   - level: 评论层级（0=一级，1=二级）
    ///   - showExpandReplies: 是否展示"展开N条回复"按钮
    private func calculateCellHeight(text: String, hasImage: Bool, level: Int, showExpandReplies: Bool = false) -> CGFloat {
        let screenWidth = UIScreen.main.bounds.width
        let textWidth: CGFloat
        if level == 0 {
            // 右侧与屏幕间距 16，与头像距离 40+12，共 68
            textWidth = screenWidth - 16 - 40 - 12 - 16
        } else {
            textWidth = screenWidth - 16 - 32 - 8 - 44
        }
        let font = UIFont.systemFont(ofSize: 14)
        let textHeight = (text as NSString).boundingRect(
            with: CGSize(width: textWidth, height: CGFloat.greatestFiniteMagnitude),
            options: [.usesLineFragmentOrigin, .usesFontLeading],
            attributes: [.font: font],
            context: nil
        ).height

        var height: CGFloat = 0
        if level == 0 {
            height = 16 + 20 + 8 + textHeight // 顶部 + 用户名行 + 间距 + 文本
            if hasImage { height += 8 + 68 }
            // 时间行：与上方间距 8 + label 高度 12
            height += 8 + 12
            // 展开回复按钮已移出 CommentCell，不再计算
            height += 8 // bottom margin
        } else {
            height = 0 + 20 + 6 + textHeight
            if hasImage { height += 6 + 60 }
            height += 4
        }
        // 最小高度保护，避免信息被裁剪
        if height < 60 { height = 60 }
        return ceil(height)
    }
    
    private func setupKeyboardObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )
    }
    
    @objc private func keyboardWillShow(_ notification: Notification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
        let keyboardHeight = keyboardFrame.height
        lastKeyboardHeight = keyboardHeight
        if inputPanelState == .none { // 仅在当前非键盘/自定义面板状态时切换
            inputPanelState = .keyboard
        }
        commentInputBarBottomConstraint?.constant = -keyboardHeight
        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded()
        }
    }
    
    @objc private func keyboardWillHide(_ notification: Notification) {
        if inputPanelState == .emoji || inputPanelState == .atPanel {
            // 不回到底部，等待自定义面板弹出
        } else {
            inputPanelState = .none
        }
    }
    
    // 添加下拉手势处理
    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)
        let velocity = gesture.velocity(in: view)
        
        switch gesture.state {
        case .changed:
            // 只允许向下拖动
            if translation.y > 0 {
                containerView.transform = CGAffineTransform(translationX: 0, y: translation.y)
            }
            
        case .ended:
            // 如果下拉速度够快或者下拉距离超过一定值，就关闭视图
            if velocity.y > 1000 || translation.y > 200 {
                UIView.animate(withDuration: 0.3, animations: {
                    self.containerView.transform = CGAffineTransform(translationX: 0, y: self.containerView.frame.height)
                }) { _ in
                    self.dismiss(animated: false)
                }
            } else {
                // 否则回弹
                UIView.animate(withDuration: 0.3) {
                    self.containerView.transform = .identity
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - 发送按钮事件
     @objc private func sendButtonTapped() {
         guard !isSending else { return }
         let rawText = inputFieldRef?.text ?? ""
         let contentText = processContentForSending(from: rawText).trimmingCharacters(in: .whitespacesAndNewlines)
         // 没有文字且没有选图 → 不发送
         guard !contentText.isEmpty || selectedImage != nil else { return }

         // 发送前先收起所有键盘和面板
         inputPanelState = .none

         isSending = true

         // 显示 HUD
         let loadingHUD = UIActivityIndicatorView(style: .large)
         loadingHUD.color = UIColor(hex: "#FF6236")
         loadingHUD.center = view.center
         loadingHUD.startAnimating()
         view.addSubview(loadingHUD)

         // 内部闭包：真正调用发送 API
         func performSend(with imageURLs: [String]?) {
             if let pid = replyPid {
                 APIManager.shared.replyComment(worksId: videoId, pid: pid, commentDesc: contentText, commentImg: imageURLs, pcustomerId: replyPcustomerId) { [weak self] result in
                     self?.handleReplySendResult(result)
                 }
             } else {
                 APIManager.shared.sendComment(worksId: videoId, commentDesc: contentText, commentImg: imageURLs) { [weak self] result in
                     self?.handleCommentSendResult(result, onSuccess: {
                         self?.refreshAfterSend()
                     })
                 }
             }
         }

         // 发送完成统一移除 HUD
         func finishLoading() {
             DispatchQueue.main.async { loadingHUD.removeFromSuperview() }
         }

         // 如果携带图片，先上传至七牛
         if let img = selectedImage, let data = img.jpegData(compressionQuality: 0.85) {
             APIManager.shared.uploadFileQNRaw(files: [data]) { [weak self] uploadResult in
                 switch uploadResult {
                 case .success(let resp):
                     if resp.status == 200, let url = resp.data?.first?.data.first {
                         performSend(with: [url])
                         finishLoading()
                     } else {
                         DispatchQueue.main.async { self?.showToast(resp.errMsg ?? "图片上传失败") }
                         self?.isSending = false
                         finishLoading()
                     }
                 case .failure(let error):
                     DispatchQueue.main.async { self?.showToast("图片上传失败：\(error.localizedDescription)") }
                     finishLoading()
                     self?.isSending = false
                 }
             }
         } else {
             performSend(with: nil)
             finishLoading()
         }
     }

    // 处理发送成功或失败（无返回数据，仅提示）
    private func handleCommentSendResult(_ result: Result<BaseResponse, APIError>, onSuccess: @escaping () -> Void) {
        DispatchQueue.main.async {
            self.isSending = false
            switch result {
            case .success(let resp):
                if resp.status == 200 {
                    // 清理输入框内容
                    self.inputFieldRef?.text = ""
                    self.selectedImage = nil
                    self.removeImagePreview()
                    self.updateSendButtonState()

                    // 发送成功后保持键盘收起状态，不重新聚焦
                    // inputPanelState 已经在发送前设置为 .none，这里不需要改变

                    self.showToast("发送成功")
                    onSuccess()
                } else {
                    self.showToast(resp.errMsg.isEmpty == false ? resp.errMsg : "发送失败")
                }
            case .failure(let error):
                self.showToast("发送失败：\(error.localizedDescription)")
            }
        }
    }

    // 处理回复评论的结果（需要用返回数据插入本地列表）
    private func handleReplySendResult(_ result: Result<CommentSingleResponse, APIError>) {
        DispatchQueue.main.async {
            self.isSending = false
            switch result {
            case .success(let resp):
                if resp.status == 200 {
                    // 清理输入框内容
                    self.inputFieldRef?.text = ""
                    self.selectedImage = nil
                    self.removeImagePreview()
                    self.updateSendButtonState()

                    // 发送成功后保持键盘收起状态，不重新聚焦
                    // inputPanelState 已经在发送前设置为 .none，这里不需要改变

                    self.showToast("发送成功")
                    // 将服务器返回的数据转换为 CommentModel
                    let model = self.convertToCommentModel(resp.data, level: 1)
                    self.insertLocalReply(model: model)
                } else {
                    self.showToast(resp.errMsg.isEmpty ? "发送失败" : resp.errMsg)
                }
            case .failure(let error):
                self.showToast("发送失败：\(error.localizedDescription)")
            }
        }
    }

    private func clearInputAfterSend() {
        inputFieldRef?.text = ""
        selectedImage = nil
        removeImagePreview()
        inputPanelState = .none
        // 更新发送按钮状态
        updateSendButtonState()
    }



    private func refreshAfterSend() {
        // 刷新整个列表显示最新评论
        comments.removeAll()
        lastCommentId = nil
        lastCreateTime = nil
        hasMoreData = true
        tableView.reloadData()
        loadComments()
    }

    // 本地插入回复
    private func insertLocalReply(model: CommentModel) {
        guard let parentIdx = selectedParentIndex else { return }
        var modelVar = model
        modelVar.cellHeight = calculateCellHeight(text: model.content, hasImage: false, level: 1)

        var insertIndex = parentIdx + 1
        while insertIndex < comments.count && comments[insertIndex].level == 1 {
            insertIndex += 1
        }
        comments.insert(modelVar, at: insertIndex)

        tableView.beginUpdates()
        tableView.insertRows(at: [IndexPath(row: insertIndex, section: 0)], with: .automatic)
        // reload parent to update button frame
        tableView.reloadRows(at: [IndexPath(row: parentIdx, section: 0)], with: .none)
        tableView.endUpdates()

        // 滚动到新回复可见
        tableView.scrollToRow(at: IndexPath(row: insertIndex, section: 0), at: .middle, animated: true)

        // 重置回复状态
        replyPid = nil
        replyTargetUsername = nil
        selectedParentIndex = nil
        clickedIndex = nil
    }
    
    // MARK: - 表情键盘切换逻辑
    @objc private func emojiButtonTapped() {
        inputPanelState = (inputPanelState == .emoji) ? .keyboard : .emoji
    }
    private func showEmojiKeyboard() {
        if emojiKeyboardView == nil {
            let emojiView = EmojiKeyboardView()
            emojiView.emojiSelected = { [weak self] emoji in
                self?.insertEmoji(emoji)
            }
            emojiKeyboardView = emojiView
            view.addSubview(emojiView)
            emojiView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                emojiView.leftAnchor.constraint(equalTo: view.leftAnchor),
                emojiView.rightAnchor.constraint(equalTo: view.rightAnchor),
                emojiView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
                emojiView.heightAnchor.constraint(equalToConstant: lastKeyboardHeight)
            ])
            // 立即隐藏并布局，防止初次动画
            emojiView.isHidden = true
            view.layoutIfNeeded()
        }
        // 只在未显示时做动画
        if emojiKeyboardView?.isHidden ?? true {
            emojiKeyboardView?.alpha = 0
            emojiKeyboardView?.isHidden = false
            commentInputBarBottomConstraint?.constant = -lastKeyboardHeight
            isEmojiKeyboardVisible = true
            UIView.animate(withDuration: 0.25) {
                self.emojiKeyboardView?.alpha = 1
                self.view.layoutIfNeeded()
            }
        }
    }
    private func hideEmojiKeyboard(animated: Bool) {
        guard let emojiKeyboardView = emojiKeyboardView, !emojiKeyboardView.isHidden else { return }
        if animated {
            UIView.animate(withDuration: 0.25, animations: {
                emojiKeyboardView.alpha = 0
                self.view.layoutIfNeeded()
            }) { _ in
                emojiKeyboardView.isHidden = true
                self.isEmojiKeyboardVisible = false
                self.inputFieldRef?.becomeFirstResponder()
            }
        } else {
            emojiKeyboardView.isHidden = true
            self.isEmojiKeyboardVisible = false
            self.inputFieldRef?.becomeFirstResponder()
        }
    }
    private func insertEmoji(_ emoji: String) {
        guard let inputField = inputFieldRef else { return }
        if let selectedRange = inputField.selectedTextRange {
            inputField.replace(selectedRange, withText: emoji)
        } else {
            inputField.text.append(emoji)
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    private func updateInputPanelState(from old: InputPanelState, to new: InputPanelState) {
        // 动态调整输入bar高度
        let baseHeight: CGFloat = 132 + (selectedImage != nil ? previewExtraHeight : 0)
        switch new {
        case .none:
            // 收起键盘时，显示装饰输入框，隐藏真实输入框
            commentInputBarHeightConstraint?.constant = baseHeight + WindowUtil.safeAreaBottom
            inputFieldRef?.resignFirstResponder()
            hideEmojiKeyboard(animated: true)
            // hideAtPanel() // 预留
            commentInputBarBottomConstraint?.constant = 0
            // 重置占位文字 & 回复目标
            replyTargetUsername = nil

            // 显示装饰输入框，隐藏真实输入框
            decorativeInputBar.isHidden = false
            commentInputBar.isHidden = true

            // 更新tableView约束到装饰输入框
            tableView.snp.remakeConstraints { make in
                make.top.equalTo(headerView.snp.bottom)
                make.left.right.equalTo(containerView)
                make.bottom.equalTo(decorativeInputBar.snp.top)
            }

            UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
        case .keyboard:
            commentInputBarHeightConstraint?.constant = baseHeight
            inputFieldRef?.becomeFirstResponder()
            if old == .emoji { // 如果是从表情盘切换到键盘
                hideEmojiKeyboard(animated: true) // 显式收起表情盘
            }
            // hideAtPanel()
        case .emoji:
            commentInputBarHeightConstraint?.constant = baseHeight
            inputFieldRef?.resignFirstResponder()
            showEmojiKeyboard()
            // hideAtPanel()
        case .atPanel:
            commentInputBarHeightConstraint?.constant = baseHeight
            inputFieldRef?.becomeFirstResponder() // 保持键盘
            hideEmojiKeyboard(animated: true)
            showAtPanel()
        }
    }
    
    // MARK: - 输入框事件处理 & Mention 删除控制
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        // 回车发送
        if text == "\n" {
            sendButtonTapped()
            return false
        }

        guard let attrStr = textView.attributedText else { return true }

        // 处理删除逻辑：text 为空表示删除
        if text.isEmpty {
            var targetRange = range
            // backspace 情况：range.length == 0
            if range.length == 0, range.location > 0 {
                targetRange.location -= 1
                targetRange.length = 1
            }

            // 检查是否落在 mentionId 富文本范围
            let mentionAttr = NSAttributedString.Key("mentionId")
            // 越界保护
            guard targetRange.location < attrStr.length else { return true }
            var effective = NSRange(location: 0, length: 0)
            if attrStr.attribute(mentionAttr, at: targetRange.location, effectiveRange: &effective) != nil {
                // 扩展：同时删除尾部零宽空格(若存在)
                let fullRange = NSRange(location: effective.location, length: effective.length + 1 <= attrStr.length && (attrStr.string as NSString).character(at: effective.location + effective.length) == 0x200B ? effective.length + 1 : effective.length)
                let storage = textView.textStorage
                storage.beginEditing()
                storage.replaceCharacters(in: fullRange, with: "")
                storage.endEditing()
                textView.selectedRange = NSRange(location: effective.location, length: 0)
                rebuildMentionDict()
                return false
            }
        } else {
            // 禁止在 mention 高亮块内部插入内容
            let mentionAttr = NSAttributedString.Key("mentionId")
            guard range.location < attrStr.length else { return true }
            var eff = NSRange(location: 0, length: 0)
            if attrStr.attribute(mentionAttr, at: range.location, effectiveRange: &eff) != nil {
                return false
            }
        }
        return true
    }

    private func rebuildMentionDict() {
        mentionedUserDict.removeAll()
        guard let attr = inputFieldRef?.attributedText else { return }
        attr.enumerateAttribute(NSAttributedString.Key("mentionId"), in: NSRange(location: 0, length: attr.length), options: []) { value, range, _ in
            if let id = value as? String {
                let raw = (attr.string as NSString).substring(with: range)
                let trimmed = raw.replacingOccurrences(of: "@", with: "").trimmingCharacters(in: CharacterSet(charactersIn: "\u{200B}"))
                mentionedUserDict[id] = trimmed
            }
        }
    }

    // MARK: - UITableViewDelegate & DataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return cachedRowMap.count
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let item = cachedRowMap[indexPath.row]
        switch item.0 {
        case .expand:
            return 32 // 固定高度
        default:
            // 直接交由 Auto Layout 计算高度，避免手动估算值过小导致内容被压缩
            return UITableView.automaticDimension
        }
    }

    // 新增：数据源必备方法，返回具体 Cell
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let item = cachedRowMap[indexPath.row]
        let model = comments[item.1]
        switch item.0 {
        case .comment:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "CommentCell", for: indexPath) as? CommentCell else { return UITableViewCell() }
            cell.configure(with: model)
            // 头像点击回调
            cell.avatarTapCallback = { [weak self] in
                self?.navigateToPersonalHomepage(userId: model.customerId)
            }
            cell.likeCallback = { [weak self] in
                self?.toggleLike(forRow: indexPath.row)
            }
            cell.dislikeCallback = { [weak self] in
                self?.toggleDislike(forRow: indexPath.row)
            }
            // 长按弹窗
            cell.longPressCallback = { [weak self] in
                guard let self = self else { return }
                let isOwner = model.isMine
                let actions: [CommentActionType] = [.reply, .copy, .report, .delete]
                let sheet = CommentActionSheet(actions: actions, isOwner: isOwner) { [weak self] action in
                    self?.handleCommentAction(action, for: model, at: indexPath)
                }
                sheet.show(in: self.view)
            }
            return cell
        case .expand:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "ExpandRepliesCell", for: indexPath) as? ExpandRepliesCell else { return UITableViewCell() }
            let loaded = self.replyCursorTracker[model.id]?.loadedCount ?? 0
            let remaining = max(model.childCount - loaded, 0)
            let title = loaded == 0 ? "展开\(remaining)条回复↓" : "展开更多"
            cell.configure(title: title)
            cell.tapCallback = { [weak self] in
                self?.handleExpandReplies(forParentIndex: item.1)
            }
            return cell
        case .reply:
            guard let cell = tableView.dequeueReusableCell(withIdentifier: replyCellIdentifier, for: indexPath) as? CommentReplyCell else { return UITableViewCell() }
            cell.configure(with: model)
            cell.avatarTapCallback = { [weak self] in
                self?.navigateToPersonalHomepage(userId: model.customerId)
            }
            cell.likeCallback = { [weak self] in
                self?.toggleLike(forRow: indexPath.row)
            }
            cell.dislikeCallback = { [weak self] in
                self?.toggleDislike(forRow: indexPath.row)
            }
            // 长按弹窗
            cell.longPressCallback = { [weak self] in
                guard let self = self else { return }
                let isOwner = model.isMine
                let actions: [CommentActionType] = [.reply, .copy, .report, .delete]
                let sheet = CommentActionSheet(actions: actions, isOwner: isOwner) { [weak self] action in
                    self?.handleCommentAction(action, for: model, at: indexPath)
                }
                sheet.show(in: self.view)
            }
            return cell
        }
    }

    // 新增：去除section header/footer间距
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 0.01
    }
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 0.01
    }

    // 选中评论进行回复
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let mapItem = cachedRowMap[indexPath.row]
        if mapItem.0 == .expand { return } // 展开按钮点击已在回调处理
        let realIndex = mapItem.1
        guard realIndex < comments.count else { return }
        let model = comments[realIndex]
        clickedIndex = indexPath.row
        // 确定父评论（一级）下标
        var parentIndex = realIndex
        if model.level == 1 {
            var idx = realIndex
            while idx >= 0 {
                if comments[idx].level == 0 { parentIndex = idx; break }
                idx -= 1
            }
        }
        selectedParentIndex = parentIndex

        if model.level == 0 {
            // 回复一级 -> 二级评论
            replyTargetUsername = nil
            replyPid = Int(model.id)
            replyPcustomerId = nil
        } else {
            // 回复二级 -> 逻辑也是二级评论，但带 @ 对象
            replyTargetUsername = model.username
            replyPid = Int(comments[parentIndex].id) // 始终传一级 pid
            replyPcustomerId = model.customerId
        }
        // 聚焦输入框并弹出键盘
        inputPanelState = .keyboard
    }
    
    // 监听所有滚动视图的滚动：
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        // 评论列表的上拉加载更多
        if scrollView == tableView {
            let offsetY = scrollView.contentOffset.y
            let contentHeight = scrollView.contentSize.height
            let height = scrollView.frame.size.height
            if offsetY > contentHeight - height - 100 {
                loadComments()
            }
            return
        }

        // @ 用户搜索面板的水平分页加载
        if let mentionCollectionView = mentionCollectionView, scrollView == mentionCollectionView {
            let offsetX = scrollView.contentOffset.x + scrollView.bounds.width
            if mentionHasMore && offsetX > scrollView.contentSize.width - 40 {
                mentionPage += 1
                searchMentionUsers(keyword: mentionKeyword, page: mentionPage)
            }
            return
        }
    }
    
    // MARK: - UIGestureRecognizerDelegate
    // 修改手势代理方法
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        // 只有点击背景遮罩时才触发关闭
        return touch.view == self.backgroundView
    }
    
    // MARK: - UITextViewDelegate
    func textViewDidBeginEditing(_ textView: UITextView) {
        // 输入框获得焦点时，强制切换到键盘状态
        inputPanelState = .keyboard
    }
    
    // MARK: - 展开二级评论
    private func handleExpandReplies(at indexPath: IndexPath) {
        guard indexPath.row < comments.count else { return }
        var parentComment = comments[indexPath.row]
        // 取父评论的分页游标（fromId / createTime）和已加载数量
        let cursor = replyCursorTracker[parentComment.id] ?? (nil, nil, 0)
        let fromIdForReply = cursor.lastId
        let createTimeForReply = cursor.lastTime
        let loadedCount = cursor.loadedCount
        // 第一次展开拉取 3 条，其后每次拉取 10 条
        let pageSizeForReply = (loadedCount == 0) ? 3 : 10
        // 转换 parentComment.id 为 Int
        guard let parentIdInt = Int(parentComment.id) else { return }
        isLoading = true
        APIManager.shared.getVideoComment(
            worksId: videoId,
            size: pageSizeForReply,
            pid: parentIdInt,
            excludeId: nil,
            fromId: fromIdForReply,
            createTime: createTimeForReply
        ) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        let items = response.data.list
                        var globalMap: [String:String] = [:]
                        if let anyMap = response.data.mentionedUser {
                            if let dict = anyMap as? [String:String] { globalMap = dict }
                            else if let dict = anyMap as? [String:Any] { for (k,v) in dict { if let name = v as? String { globalMap[k] = name } } }
                        }
                        let replyModels = items.map { self.convertToCommentModel($0, level: 1, mentionMapping: globalMap) }
                        // 在扁平化数组中插入 replyModels，确保追加到已有二级评论之后
                        var insertIndex = indexPath.row + 1
                        while insertIndex < self.comments.count && self.comments[insertIndex].level == 1 {
                            insertIndex += 1
                        }
                        self.comments.insert(contentsOf: replyModels, at: insertIndex)

                        // 计算总共已加载的回复数量
                        let newLoadedCount = loadedCount + replyModels.count
                        
                        // 更新游标：记录最新一条回复的 id / createTime 和已加载数量
                        if let last = items.last {
                            self.replyCursorTracker[parentComment.id] = (last.id, last.createTime, newLoadedCount)
                        } else {
                            // 若本次已无数据，则不再显示展开按钮
                            self.replyCursorTracker[parentComment.id] = (fromIdForReply, createTimeForReply, newLoadedCount)
                        }
                        
                        // 更新 parentComment 展开按钮状态：比较已加载数量与总数
                        parentComment.showExpandReplies = newLoadedCount < parentComment.childCount

                        // 重新计算行高，确保布局正确
                        parentComment.cellHeight = self.calculateCellHeight(
                            text: parentComment.content,
                            hasImage: (parentComment.imageUrl != nil && !(parentComment.imageUrl!.isEmpty)),
                            level: parentComment.level,
                            showExpandReplies: parentComment.showExpandReplies
                        )
                        self.comments[indexPath.row] = parentComment
                        
                        print("[CommentVC] 二级评论加载进度: \(newLoadedCount)/\(parentComment.childCount)")
                        // 直接刷新整表，避免行映射错位引发崩溃
                        self.tableView.reloadData()
                    } else {
                        self.showToast("加载回复失败，请稍后重试")
                    }
                case .failure:
                    self.showToast("加载回复失败，请检查网络")
                }
            }
        }
    }

    // MARK: - Expand Button Handler
    private func handleExpandReplies(forParentIndex parentIdx: Int) {
        // 找到 Comment 数组中的父评论 IndexPath
        let indexPath = IndexPath(row: parentIdx, section: 0)
        handleExpandReplies(at: indexPath)
    }

    // timestamp(from:) 已不再使用，若后续需要可重新实现

    // MARK: - 图片选择与预览
    @objc private func imageButtonTapped() {
        var config = PHPickerConfiguration(photoLibrary: .shared())
        config.filter = .images
        config.selectionLimit = 1
        let picker = PHPickerViewController(configuration: config)
        picker.delegate = self
        present(picker, animated: true)
    }

    // PHPicker 回调
    func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
        picker.dismiss(animated: true)
        guard let itemProvider = results.first?.itemProvider else { return }
        if itemProvider.canLoadObject(ofClass: UIImage.self) {
            itemProvider.loadObject(ofClass: UIImage.self) { [weak self] image, error in
                guard let self = self else { return }
                DispatchQueue.main.async {
                    if let uiImage = image as? UIImage {
                        self.selectedImage = uiImage
                        self.imagePreviewImageView?.image = uiImage
                        self.imagePreviewContainer?.isHidden = false
                        self.imagePreviewCloseButton?.isHidden = false
                        // 调整输入框高度
                        self.commentInputBarHeightConstraint?.constant = 132 + self.previewExtraHeight + (self.inputPanelState == .none ? WindowUtil.safeAreaBottom : 0)
                        UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
                        // 更新发送按钮状态
                        self.updateSendButtonState()
                    }
                }
            }
        }
    }

    // 关闭图片预览
    @objc private func removeImagePreview() {
        selectedImage = nil
        imagePreviewImageView?.image = nil
        imagePreviewContainer?.isHidden = true
        imagePreviewCloseButton?.isHidden = true
        // 恢复输入框高度
        commentInputBarHeightConstraint?.constant = 132 + (inputPanelState == .none ? WindowUtil.safeAreaBottom : 0)
        UIView.animate(withDuration: 0.25) { self.view.layoutIfNeeded() }
        // 更新发送按钮状态
        updateSendButtonState()
    }

    // MARK: - @ 按钮事件
    @objc private func atButtonTapped() {
        guard let inputField = inputFieldRef else { return }
        // 在光标位置插入"@"并进入提及模式
        let cursor = inputField.selectedRange.location
        inputField.textStorage.replaceCharacters(in: inputField.selectedRange, with: "@")
        inputField.selectedRange = NSRange(location: cursor + 1, length: 0)

        isMentioning = true
        mentionStartLocation = cursor
        mentionKeyword = ""
        mentionPage = 0
        searchMentionUsers(keyword: "", page: 0)
        inputPanelState = .atPanel

        // 显示面板并更新占位文字（防止旧状态下不调用）
        showAtPanel()
    }

    // MARK: - 显示/隐藏提及用户面板
    private func showAtPanel() {
        if atPanelView == nil {
            let panel = UIView()
            panel.backgroundColor = .white
            view.addSubview(panel)
            panel.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                panel.leftAnchor.constraint(equalTo: view.leftAnchor),
                panel.rightAnchor.constraint(equalTo: view.rightAnchor),
                panel.bottomAnchor.constraint(equalTo: commentInputBar.topAnchor),
                panel.heightAnchor.constraint(equalToConstant: 98)
            ])

            // collectionView
            let layout = UICollectionViewFlowLayout()
            layout.scrollDirection = .horizontal
            layout.minimumLineSpacing = 12
            layout.sectionInset = UIEdgeInsets(top: 0, left: 8, bottom: 0, right: 8)
            let collection = UICollectionView(frame: .zero, collectionViewLayout: layout)
            collection.backgroundColor = .white
            collection.showsHorizontalScrollIndicator = false
            collection.dataSource = self
            collection.delegate = self
            collection.register(MentionUserCell.self, forCellWithReuseIdentifier: "MentionUserCell")
            panel.addSubview(collection)
            collection.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                collection.topAnchor.constraint(equalTo: panel.topAnchor),
                collection.bottomAnchor.constraint(equalTo: panel.bottomAnchor),
                collection.leftAnchor.constraint(equalTo: panel.leftAnchor),
                collection.rightAnchor.constraint(equalTo: panel.rightAnchor)
            ])

            atPanelView = panel
            mentionCollectionView = collection
        }
        atPanelView?.isHidden = false
        inputFieldRef?.placeholder = mentionPlaceholder
    }

    private func hideAtPanel() {
        atPanelView?.isHidden = true
        // 恢复占位文字
        if let name = replyTargetUsername, !name.isEmpty {
            inputFieldRef?.placeholder = "回复 @\(name)"
        } else {
            inputFieldRef?.placeholder = defaultPlaceholder
        }
    }

    // MARK: - 搜索用户（带防抖）
    private func scheduleSearch(keyword: String) {
        debounceWorkItem?.cancel()
        let work = DispatchWorkItem { [weak self] in
            self?.searchMentionUsers(keyword: keyword, page: 0)
        }
        debounceWorkItem = work
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5, execute: work)
    }

    private func searchMentionUsers(keyword: String, page: Int) {
        APIManager.shared.searchUser(keywords: keyword, page: page, size: mentionPageSize) { [weak self] result in
            guard let self = self else { return }
            switch result {
            case .success(let data):
                let newList = data.data.list
                self.mentionTotal = data.data.total
                if page == 0 { self.mentionUsersData = newList } else { self.mentionUsersData.append(contentsOf: newList) }
                self.mentionHasMore = self.mentionUsersData.count < self.mentionTotal && !newList.isEmpty
                DispatchQueue.main.async { self.mentionCollectionView?.reloadData() }
            case .failure:
                if page == 0 { self.mentionUsersData = [] }
                self.mentionHasMore = false
                DispatchQueue.main.async { self.mentionCollectionView?.reloadData() }
            }
        }
    }

    // MARK: - 插入选中用户
    private func insertMentionUser(_ user: UserSearchResultsItem) {
        guard let textView = inputFieldRef else { return }
        let cursorPosition = textView.selectedRange.location
        let mentionRange = NSRange(location: mentionStartLocation, length: cursorPosition - mentionStartLocation)

        let mentionDisplay = "@" + user.nickName
        let mentionAttr = NSMutableAttributedString(string: mentionDisplay)
        mentionAttr.addAttribute(.foregroundColor, value: UIColor.systemBlue, range: NSRange(location: 0, length: mentionDisplay.count))
        mentionAttr.addAttribute(NSAttributedString.Key("mentionId"), value: user.customerId, range: NSRange(location: 0, length: mentionDisplay.count))

        let separatorAttr = NSAttributedString(string: "\u{200B}")

        let storage = textView.textStorage
        storage.beginEditing()
        storage.replaceCharacters(in: mentionRange, with: mentionAttr)
        storage.insert(separatorAttr, at: mentionStartLocation + mentionDisplay.count)
        storage.endEditing()

        textView.selectedRange = NSRange(location: mentionStartLocation + mentionDisplay.count + 1, length: 0)

        mentionedUserDict[user.customerId] = user.nickName
        isMentioning = false
        // 切换回键盘状态并保持焦点
        inputPanelState = .keyboard
        inputFieldRef?.becomeFirstResponder()
        hideAtPanel()
    }

    // MARK: - 处理输入变化
    func textViewDidChange(_ textView: UITextView) {
        let cursor = textView.selectedRange.location
        let text = textView.text ?? ""
        let nsText = text as NSString

        // 更新发送按钮状态
        updateSendButtonState()

        if isMentioning {
            if mentionStartLocation >= nsText.length || nsText.character(at: mentionStartLocation) != 64 { // "@"
                isMentioning = false
                hideAtPanel()
                return
            }
            let range = NSRange(location: mentionStartLocation + 1, length: cursor - mentionStartLocation - 1)
            if let swiftRange = Range(range, in: text) {
                mentionKeyword = String(text[swiftRange])
                scheduleSearch(keyword: mentionKeyword)
            }
        } else if cursor > 0, nsText.character(at: cursor - 1) == 64 { // 新输入@
            isMentioning = true
            mentionStartLocation = cursor - 1
            mentionKeyword = ""
            mentionPage = 0
            searchMentionUsers(keyword: "", page: 0)
            showAtPanel()
        }
    }

    // MARK: - 更新发送按钮状态
    private func updateSendButtonState() {
        guard let sendButton = sendButtonRef else { return }

        let text = inputFieldRef?.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        let hasText = !text.isEmpty
        let hasImage = selectedImage != nil
        let canSend = hasText || hasImage

        sendButton.isEnabled = canSend

        if canSend {
            // 有内容时：完全不透明的橙色
            sendButton.backgroundColor = UIColor(hex: "#FF8F1F")
            sendButton.setTitleColor(.white, for: .normal)
        } else {
            // 无内容时：半透明的橙色
            sendButton.backgroundColor = UIColor(hex: "#FF8F1F", alpha: 0.5)
            sendButton.setTitleColor(UIColor.white.withAlphaComponent(0.7), for: .normal)
        }
    }

    // MARK: - UICollectionView DataSource & Delegate
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return mentionUsersData.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MentionUserCell", for: indexPath) as! MentionUserCell
        cell.configure(with: mentionUsersData[indexPath.item])
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        insertMentionUser(mentionUsersData[indexPath.item])
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        return CGSize(width: 74, height: 70)
    }

    // MARK: - 提及用户 Cell
    class MentionUserCell: UICollectionViewCell {
        private let avatarImageView = UIImageView()
        private let nameLabel = UILabel()
        override init(frame: CGRect) {
            super.init(frame: frame)
            avatarImageView.layer.cornerRadius = 24
            avatarImageView.clipsToBounds = true
            avatarImageView.translatesAutoresizingMaskIntoConstraints = false
            nameLabel.font = UIFont.systemFont(ofSize: 13)
            nameLabel.textAlignment = .center
            nameLabel.translatesAutoresizingMaskIntoConstraints = false
            contentView.addSubview(avatarImageView)
            contentView.addSubview(nameLabel)
            NSLayoutConstraint.activate([
                avatarImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 4),
                avatarImageView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
                avatarImageView.widthAnchor.constraint(equalToConstant: 48),
                avatarImageView.heightAnchor.constraint(equalToConstant: 48),
                nameLabel.topAnchor.constraint(equalTo: avatarImageView.bottomAnchor, constant: 4),
                nameLabel.leftAnchor.constraint(equalTo: contentView.leftAnchor),
                nameLabel.rightAnchor.constraint(equalTo: contentView.rightAnchor),
                nameLabel.heightAnchor.constraint(equalToConstant: 20)
            ])
        }
        required init?(coder: NSCoder) { super.init(coder: coder) }
        func configure(with user: UserSearchResultsItem) {
            nameLabel.text = user.nickName
            if let url = URL(string: user.wxAvator), !user.wxAvator.isEmpty {
                avatarImageView.kf.setImage(with: url, placeholder: UIImage(named: "avatar_placeholder"))
            } else {
                avatarImageView.image = UIImage(named: "avatar_placeholder")
            }
        }
    }

    /// 处理文本，将 @用户名 转为 @id\，并移除零宽空格
    private func processContentForSending(from plain: String) -> String {
        var processed = plain.replacingOccurrences(of: "\u{200B}", with: "")
        // 按用户名长度降序，避免子串冲突
        let sorted = mentionedUserDict.sorted { $0.value.count > $1.value.count }
        for (id, name) in sorted {
            let pattern = "@" + NSRegularExpression.escapedPattern(for: name)
            processed = processed.replacingOccurrences(of: pattern, with: "@" + id + "\\")
        }
        return processed
    }

    // MARK: - 点赞 / 不喜欢 操作
    private func toggleLike(forRow mapRow: Int) {
        let item = cachedRowMap[mapRow]
        let idx = item.1
        guard idx < comments.count, let commentId = Int(comments[idx].id) else { return }
        var model = comments[idx]
        let newLikeState = !model.isLiked
        let operateValue = newLikeState ? 1 : 2
        // 先进行本地更新，提供即时反馈
        model.isLiked = newLikeState
        if newLikeState {
            model.likes += 1
            if model.isDisliked { model.isDisliked = false; model.dislikes = max(model.dislikes - 1, 0) }
        } else {
            model.likes = max(model.likes - 1, 0)
        }
        comments[idx] = model
        tableView.reloadRows(at: [IndexPath(row: mapRow, section: 0)], with: .none)

        APIManager.shared.operateWorkComment(commentId: commentId, operateType: 1, operateValue: operateValue, worksId: videoId) { [weak self] result in
            if case .failure(_) = result {
                // 失败回滚
                DispatchQueue.main.async { self?.refreshCommentRow(idx: idx, mapRow: mapRow) }
            }
        }
    }

    private func toggleDislike(forRow mapRow: Int) {
        let item = cachedRowMap[mapRow]
        let idx = item.1
        guard idx < comments.count, let commentId = Int(comments[idx].id) else { return }
        var model = comments[idx]
        let newDislikeState = !model.isDisliked
        let operateValue = newDislikeState ? 1 : 2
        model.isDisliked = newDislikeState
        if newDislikeState {
            model.dislikes += 1
            if model.isLiked { model.isLiked = false; model.likes = max(model.likes - 1, 0) }
        } else {
            model.dislikes = max(model.dislikes - 1, 0)
        }
        comments[idx] = model
        tableView.reloadRows(at: [IndexPath(row: mapRow, section: 0)], with: .none)

        APIManager.shared.operateWorkComment(commentId: commentId, operateType: 2, operateValue: operateValue, worksId: videoId) { [weak self] result in
            if case .failure(_) = result {
                DispatchQueue.main.async { self?.refreshCommentRow(idx: idx, mapRow: mapRow) }
            }
        }
    }

    private func refreshCommentRow(idx: Int, mapRow: Int) {
        guard idx < comments.count else { return }
        // 重新拉取评论数据或简单刷新状态（此处简单刷新）
        tableView.reloadRows(at: [IndexPath(row: mapRow, section: 0)], with: .none)
    }

    // MARK: - 导航到个人主页
    private func navigateToPersonalHomepage(userId: String) {
        let personalVC = PersonalHomepageViewController()
        personalVC.userId = userId
        if let nav = self.navigationController {
            nav.pushViewController(personalVC, animated: true)
        } else {
            let navVC = UINavigationController(rootViewController: personalVC)
            navVC.modalPresentationStyle = .fullScreen
            self.present(navVC, animated: true)
        }
    }

    // 新增：获取当前用户ID方法（请根据实际用户体系实现）
    private func getCurrentUserId() -> String {
        // TODO: 替换为实际获取当前登录用户ID的逻辑
        return "当前用户ID"
    }

    // 新增：处理评论操作弹窗回调
    private func handleCommentAction(_ action: CommentActionType, for model: CommentModel, at indexPath: IndexPath) {
        switch action {
        case .reply:
            // 触发回复逻辑（与didSelectRowAt一致）
            let realIndex = indexPath.row
            clickedIndex = indexPath.row
            var parentIndex = realIndex
            if model.level == 1 {
                var idx = realIndex
                while idx >= 0 {
                    if comments[idx].level == 0 { parentIndex = idx; break }
                    idx -= 1
                }
            }
            selectedParentIndex = parentIndex
            if model.level == 0 {
                replyTargetUsername = nil
                replyPid = Int(model.id)
                replyPcustomerId = nil
            } else {
                replyTargetUsername = model.username
                replyPid = Int(comments[parentIndex].id)
                replyPcustomerId = model.customerId
            }
            inputPanelState = .keyboard
        case .copy:
            UIPasteboard.general.string = model.content
            showToast("已复制")
        case .report:
            // 简化举报流程：直接本地 HUD 提示
            showToast("举报成功")
        case .delete:
            // 调用删除接口并在成功后本地删除评论
            confirmDeleteComment(model: model, mapRow: indexPath.row)
        }
    }

    // MARK: - 删除评论
    /// 弹出确认框后删除评论
    private func confirmDeleteComment(model: CommentModel, mapRow: Int) {
        guard let commentId = Int(model.id) else { return }

        let alert = UIAlertController(title: "删除评论", message: "确定要删除这条评论吗？", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "取消", style: .cancel))
        alert.addAction(UIAlertAction(title: "删除", style: .destructive, handler: { [weak self] _ in
            self?.requestDeleteComment(commentId: commentId, model: model, mapRow: mapRow)
        }))
        present(alert, animated: true)
    }

    /// 发起接口请求删除评论
    private func requestDeleteComment(commentId: Int, model: CommentModel, mapRow: Int) {
        APIManager.shared.deleteComment(commentId: commentId) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let resp):
                    if resp.status == 200 {
                        self.applyLocalDelete(for: model, mapRow: mapRow)
                        self.showToast("已删除")
                    } else {
                        self.showToast(resp.errMsg.isEmpty ? "删除失败" : resp.errMsg)
                    }
                case .failure(let error):
                    self.showToast("删除失败：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 根据评论层级删除本地数据并更新 UI
    private func applyLocalDelete(for model: CommentModel, mapRow: Int) {
        let idxInComments = cachedRowMap[mapRow].1
        guard idxInComments < comments.count else { return }

        var indicesToRemove: [Int] = [idxInComments]

        if model.level == 0 {
            // 一级评论：同时删除其所有二级回复
            var next = idxInComments + 1
            while next < comments.count && comments[next].level == 1 {
                indicesToRemove.append(next)
                next += 1
            }
            // 更新评论总数
            commentCount = max(commentCount - 1, 0)
            titleLabel.text = "评论 \(commentCount)"
        } else {
            // 二级评论：需要更新父评论的 childCount
            var parentIdx = idxInComments - 1
            while parentIdx >= 0 && comments[parentIdx].level != 0 { parentIdx -= 1 }
            if parentIdx >= 0 {
                var parent = comments[parentIdx]
                parent.childCount = max(parent.childCount - 1, 0)
                // 重新计算父评论高度，防止布局问题
                parent.cellHeight = calculateCellHeight(text: parent.content, hasImage: (parent.imageUrl != nil && !(parent.imageUrl!.isEmpty)), level: parent.level, showExpandReplies: parent.showExpandReplies)
                comments[parentIdx] = parent
            }
        }

        // 先根据当前 rowMap 记录需要删除的行
        let rowsToDelete = cachedRowMap.enumerated().filter { indicesToRemove.contains($0.element.1) }.map { IndexPath(row: $0.offset, section: 0) }

        // 按降序删除数据，避免索引错位
        for i in indicesToRemove.sorted(by: >) {
            comments.remove(at: i)
        }

        // 更新游标追踪器，防止展开时崩溃
        if model.level == 0 {
            replyCursorTracker.removeValue(forKey: model.id)
        }

        tableView.beginUpdates()
        tableView.deleteRows(at: rowsToDelete, with: .automatic)
        // 如果涉及父评论信息变化，刷新该行
        if model.level == 1 {
            // 重新计算行映射
            let newMap = cachedRowMap
            // 找到父评论在 comments 中的索引
            var search = idxInComments - 1
            var parentId: String? = nil
            while search >= 0 {
                if comments[search].level == 0 { parentId = comments[search].id; break }
                search -= 1
            }
            if let pid = parentId, let parentMapRow = newMap.firstIndex(where: { $0.0 == .comment && comments[$0.1].id == pid }) {
                tableView.reloadRows(at: [IndexPath(row: parentMapRow, section: 0)], with: .none)
            }
        }
        tableView.endUpdates()
    }
}
